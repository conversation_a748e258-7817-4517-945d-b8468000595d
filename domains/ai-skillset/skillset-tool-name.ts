export const SkillsetNameMap = {
  bika_app_builder: 'bika-app-builder',
  bika_ai_page: 'bika-ai-page',
  bika_space: 'bika-space',
  bika_database: 'bika-database',
  bika_datasets: 'bika-datasets',
  bika_automation: 'bika-automation',
  bika_image: 'bika-image',
  bika_media: 'bika-media',
  // export const DatabaseSkillsetNameEnum = z.enum([
  //   'list_records',
  //   'get_database_detail',
  //   'aggregate_records',
  //   'get_fields_schema',
  //   // 'get_select_field_options',
  //   'create_record',
  // ]);
  // import { z } from 'zod';
  //   includes: ['bika_search_images', 'bika_search_pages'],
  // },
  // {
  //   kind: 'preset',
  //   key: 'bika-office',
  //   includes: ['generate_slides'],

  // export const UserSkillsetEnum = z.enum(['list_users']);

  // export const UserSkillsetName = UserSkillsetEnum.Enum;

  // export const UnitSkillsetNameEnum = z.enum(['list_members', 'list_teams', 'list_roles']);

  // export const UnitSkillsetName = UnitSkillsetNameEnum.Enum;

  // export const NodeSkillsetNameEnum = z.enum(['get_node_info', 'list_nodes']);

  // export const NodeSkillsetName = NodeSkillsetNameEnum.Enum;
};
